<?php
namespace app\api\controller;

use app\api\controller\BaseController;
use app\common\constants\PaymentStatus;

class TransactionController extends BaseController{

	/**
	 * 提现
	 */
	public function draw(){
		$data = model('UserWithdrawals')->draw();
		return json($data);
	}


	/**
	 * 提现记录
	 */
	public function getDrawRecord(){
		$data = model('UserWithdrawals')->getUserWithdrawalsList();
		return json($data);
	}
	
	/**
	 * 渠道充值（支持多语言）
	 */
	public function getRechargetype(){
		$lang = (input('post.lang')) ? input('post.lang') : 'id'; // 获取语言参数，默认印尼语（与系统一致）
		$data = model('RechangeType')->getRechargetype($lang);
		return json($data);
	}

	/**
	 * 获取代付渠道列表（前端用户选择）- 与充值系统保持一致
	 */
	public function getWithdrawaltype(){
		//获取参数
		$token 		= input('post.token/s');
		$userArr	= explode(',',auth_code($token,'DECODE'));
		$uid		= $userArr[0];//uid
		$username 	= $userArr[1];//username
		$lang		= input('post.lang') ?: 'id';	// 语言类型
		$param 		= input('post.');

		//验证用户
		$userInfo = model('Users')->where('id',$uid)->where('username',$username)->find();
		if(!$userInfo){
			$data['code'] = 0;
			if($lang=='cn'){
				$data['code_dec']	= '用户不存在';
			}elseif($lang=='en'){
				$data['code_dec']	= 'User does not exist';
			}elseif($lang=='id'){
				$data['code_dec']	= 'Pengguna tidak ada';
			}
			return json($data);
		}

		try {
			//定义查询条件
			$where = array();
			$where['state'] = 1;
			//客户端类型
			$where['type'] = (isset($param['type']) && strtolower($param['type']) == 'app') ? 'app' : 'pc';

			$channels = model('WithdrawalChannel')->field('id,name,code,mode,min_amount,max_amount,fee_rate,config_json')->where($where)->order('sort','asc')->select()->toArray();

			if(!$channels){
				$data['code'] = 0;
				if($lang=='cn'){
					$data['code_dec']	= '没有可用的代付渠道';
				}elseif($lang=='en'){
					$data['code_dec']	= 'No available withdrawal channels';
				}elseif($lang=='id'){
					$data['code_dec']	= 'Tidak ada saluran penarikan yang tersedia';
				}
				return json($data);
			}

			$result = [];
			foreach ($channels as $channel) {
				$channelData = [
					'id' => $channel['id'],
					'name' => $channel['name'],
					'code' => $channel['code'],
					'mode' => $channel['mode'],
					'min_amount' => floatval($channel['min_amount']),
					'max_amount' => floatval($channel['max_amount']),
					'fee_rate' => floatval($channel['fee_rate'])
				];

				// 如果有配置JSON，解析并添加支持的支付方式
				if($channel['config_json']){
					$config = json_decode($channel['config_json'], true);
					if($config){
						$channelData['config'] = $config;
						// 根据不同模式提供支付方式选项
						if($channel['mode'] == 'jaya_pay' && isset($config['payment_methods'])){
							$channelData['payment_methods'] = $config['payment_methods'];
						}
						if($channel['mode'] == 'watchPay' && isset($config['countries'])){
							$channelData['countries'] = $config['countries'];
						}
					}
				}

				$result[] = $channelData;
			}

			$data['code'] = 1;
			$data['data'] = $result;
			if($lang=='cn'){
				$data['code_dec'] = '获取成功';
			}elseif($lang=='en'){
				$data['code_dec'] = 'Success';
			}elseif($lang=='id'){
				$data['code_dec'] = 'Berhasil';
			}

			return json($data);

		} catch (\Exception $e) {
			$data['code'] = 0;
			if($lang=='cn'){
				$data['code_dec'] = '获取代付渠道失败';
			}elseif($lang=='en'){
				$data['code_dec'] = 'Failed to get withdrawal channels';
			}elseif($lang=='id'){
				$data['code_dec'] = 'Gagal mendapatkan saluran penarikan';
			}
			return json($data);
		}
	}

	/**
	 * 统一提现处理接口 - 与充值系统保持一致的架构
	 */
	public function unifiedWithdrawal(){
		$param = input('post.');

		try {
			// 基础参数验证
			$required = ['token', 'channel_id', 'amount'];
			foreach ($required as $field) {
				if (!isset($param[$field]) || empty($param[$field])) {
					return json(['code' => 0, 'msg' => "缺少必要参数: {$field}"]);
				}
			}

			// 验证用户token
			$userArr = explode(',', auth_code($param['token'], 'DECODE'));
			$uid = $userArr[0] ?? 0;
			if (empty($uid)) {
				return json(['code' => 0, 'msg' => '用户登录信息无效']);
			}

			// 获取渠道信息 - 从代付渠道表获取
			$channel = model('WithdrawalChannel')->where(['id' => $param['channel_id'], 'state' => 1])->find();
			if (!$channel) {
				return json(['code' => 0, 'msg' => '代付渠道不存在或已禁用']);
			}

			// 验证金额范围
			$amount = floatval($param['amount']);
			if ($amount < $channel['min_amount'] || $amount > $channel['max_amount']) {
				return json(['code' => 0, 'msg' => '提现金额超出限制范围']);
			}

			// 记录统一提现日志
			\think\facade\Log::info('Unified withdrawal request: ' . json_encode([
				'uid' => $uid,
				'channel_id' => $param['channel_id'],
				'channel_mode' => $channel['mode'],
				'amount' => $amount
			]));

			// 根据渠道类型分流处理
			switch ($channel['mode']) {
				case 'watchPay':
					return $this->handleWatchPayWithdrawal($param, $channel);

				case 'jaya_pay':
					return $this->handleJayaPayWithdrawal($param, $channel);

				case 'traditional':
					return $this->handleTraditionalWithdrawal($param, $channel);
				default:
					return $this->handleTraditionalWithdrawal($param, $channel);
			}

		} catch (\Exception $e) {
			\think\facade\Log::error('Unified withdrawal error: ' . $e->getMessage());
			return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage()]);
		}
	}

	/**
	 * 处理WatchPay提现
	 */
	private function handleWatchPayWithdrawal($param, $channel)
	{
		// 创建提现记录
		$withdrawalData = $this->createWithdrawalRecord($param, $channel);
		if ($withdrawalData['code'] != 1) {
			return $withdrawalData;
		}

		// 调用代付服务
		$withdrawalService = new \app\common\service\WithdrawalService();
		$result = $withdrawalService->processWithdrawal($withdrawalData['data'], $param['channel_id']);

		if ($result['code'] == 1) {
			// 更新提现记录状态
			model('UserWithdrawals')->where('id', $withdrawalData['data']['id'])->update([
				'state' => 5, // 代付中
				'process_time' => time()
			]);
		}

		return $result;
	}

	/**
	 * 处理JayaPay提现
	 */
	private function handleJayaPayWithdrawal($param, $channel)
	{
		// 创建提现记录
		$withdrawalData = $this->createWithdrawalRecord($param, $channel);
		if ($withdrawalData['code'] != 1) {
			return $withdrawalData;
		}

		// 调用代付服务
		$withdrawalService = new \app\common\service\WithdrawalService();
		$result = $withdrawalService->processWithdrawal($withdrawalData['data'], $param['channel_id']);

		if ($result['code'] == 1) {
			// 更新提现记录状态
			model('UserWithdrawals')->where('id', $withdrawalData['data']['id'])->update([
				'state' => 5, // 代付中
				'process_time' => time()
			]);
		}

		return $result;
	}

	/**
	 * 处理传统提现
	 */
	private function handleTraditionalWithdrawal($param, $channel)
	{
		// 创建提现记录，传统提现需要人工审核
		$withdrawalData = $this->createWithdrawalRecord($param, $channel);
		if ($withdrawalData['code'] != 1) {
			return $withdrawalData;
		}

		return json([
			'code' => 1,
			'msg' => '提现申请已提交，等待审核',
			'data' => [
				'order_number' => $withdrawalData['data']['order_number'],
				'amount' => $param['amount'],
				'channel_name' => $channel['name']
			]
		]);
	}

	/**
	 * 创建提现记录
	 */
	private function createWithdrawalRecord($param, $channel)
	{
		try {
			// 验证用户token
			$userArr = explode(',', auth_code($param['token'], 'DECODE'));
			$uid = $userArr[0] ?? 0;
			$username = $userArr[1] ?? '';

			// 验证用户
			$userInfo = model('Users')->where('id', $uid)->where('username', $username)->find();
			if (!$userInfo) {
				return ['code' => 0, 'msg' => '用户不存在'];
			}

			// 验证用户余额
			$userTotal = model('UserTotal')->where('uid', $uid)->find();
			if (!$userTotal || $userTotal['balance'] < $param['amount']) {
				return ['code' => 0, 'msg' => '余额不足'];
			}

			// 获取用户银行卡信息
			$userBank = model('UserBank')->where('uid', $uid)->where('state', 1)->find();
			if (!$userBank) {
				return ['code' => 0, 'msg' => '请先绑定银行卡'];
			}

			// 生成订单号
			$orderNumber = 'W' . date('YmdHis') . rand(1000, 9999);

			// 创建提现记录
			$withdrawalData = [
				'uid' => $uid,
				'order_number' => $orderNumber,
				'trade_number' => $orderNumber,
				'bank_id' => $userBank['bid'],
				'card_number' => $userBank['card_no'],
				'card_name' => $userBank['name'],
				'price' => $param['amount'],
				'fee' => 0, // 手续费计算逻辑
				'time' => time(),
				'state' => 3, // 审核中
				'channel_id' => $param['channel_id']
			];

			$withdrawalId = model('UserWithdrawals')->insertGetId($withdrawalData);
			if (!$withdrawalId) {
				return ['code' => 0, 'msg' => '提现申请创建失败'];
			}

			// 扣除用户余额
			model('UserTotal')->where('uid', $uid)->setDec('balance', $param['amount']);

			// 添加资金流水
			model('UserTransaction')->insert([
				'uid' => $uid,
				'type' => 2, // 提现
				'money' => $param['amount'],
				'balance' => $userTotal['balance'] - $param['amount'],
				'remarks' => '提现申请：' . $orderNumber,
				'time' => time()
			]);

			$withdrawalData['id'] = $withdrawalId;
			return ['code' => 1, 'data' => $withdrawalData];

		} catch (\Exception $e) {
			\think\facade\Log::error('Create withdrawal record error: ' . $e->getMessage());
			return ['code' => 0, 'msg' => '创建提现记录失败'];
		}
	}

	/**
	 * 统一提现回调处理接口 - 与充值系统保持一致
	 */
	public function unifiedWithdrawalCallback()
	{
		$params = input('post.');

		try {
			// 记录回调日志
			\think\facade\Log::info('Unified withdrawal callback received: ' . json_encode($params));

			// 根据回调参数特征判断渠道类型
			$channelType = $this->detectWithdrawalChannelType($params);

			\think\facade\Log::info('Detected withdrawal channel type: ' . $channelType);

			switch ($channelType) {
				case 'watchPay':
					return $this->handleWatchPayWithdrawalCallback($params);

				case 'jaya_pay':
					return $this->handleJayaPayWithdrawalCallback($params);

				default:
					\think\facade\Log::error('Unknown withdrawal callback type (traditional withdrawal has no callback): ' . json_encode($params));
					echo 'fail';
					return;
			}

		} catch (\Exception $e) {
			\think\facade\Log::error('Unified withdrawal callback error: ' . $e->getMessage());
			echo 'fail';
		}
	}

	/**
	 * 检测提现回调渠道类型 - 根据官方文档参数特征
	 */
	private function detectWithdrawalChannelType($params)
	{
		// JayaPay代付回调特征 - 根据官方文档
		if (isset($params['platOrderNum']) || isset($params['platRespCode']) || isset($params['platSign'])) {
			return 'jaya_pay';
		}

		// WatchPay代付回调特征 - 根据官方文档
		if (isset($params['tradeResult']) || isset($params['merTransferId']) || isset($params['merNo'])) {
			return 'watchPay';
		}

		// 传统代付回调特征
		if (isset($params['order_number']) && isset($params['withdrawal_status'])) {
			return 'traditional';
		}

		return 'unknown';
	}

	/**
	 * 处理WatchPay提现回调
	 */
	private function handleWatchPayWithdrawalCallback($params)
	{
		try {
			// 验证WatchPay回调参数 - 根据官方文档
			if (!isset($params['merTransferId']) || !isset($params['tradeResult'])) {
				\think\facade\Log::error('WatchPay withdrawal callback params invalid');
				echo 'fail';
				return;
			}

			// 查找对应的提现记录
			$withdrawal = model('UserWithdrawals')->where('order_number', $params['merTransferId'])->find();
			if (!$withdrawal) {
				\think\facade\Log::error('WatchPay withdrawal callback: order not found - ' . $params['merTransferId']);
				echo 'fail';
				return;
			}

			// 获取渠道配置验证签名
			$channel = model('WithdrawalChannel')->where('id', $withdrawal['channel_id'])->find();
			if (!$channel || $channel['mode'] !== 'watchPay') {
				\think\facade\Log::error('WatchPay withdrawal channel not found or mode mismatch');
				echo 'fail';
				return;
			}

			// 从配置文件获取WatchPay配置并验证签名
			$watchPayConfig = $this->getWatchPayConfigForCallback();
			if (!$watchPayConfig || !$this->verifyWatchPayWithdrawalSign($params, $watchPayConfig['secret_key'] ?? '')) {
				\think\facade\Log::error('WatchPay withdrawal callback sign verify failed');
				echo 'fail';
				return;
			}

			// 处理WatchPay回调结果 - 根据官方文档
			if ($params['tradeResult'] == '1') {
				// 代付成功
				$this->handleWithdrawalSuccess($withdrawal['id']);
				echo 'success';
			} else {
				// 代付失败
				$this->handleWithdrawalFailed($withdrawal['id'], '代付失败，状态：' . $params['tradeResult']);
				echo 'success';
			}

		} catch (\Exception $e) {
			\think\facade\Log::error('WatchPay withdrawal callback error: ' . $e->getMessage());
			echo 'fail';
		}
	}

	/**
	 * 处理JayaPay提现回调
	 */
	private function handleJayaPayWithdrawalCallback($params)
	{
		try {
			// 获取回调参数
			$orderNo = $params['orderNum'] ?? '';
			$platOrderNo = $params['platOrderNum'] ?? '';
			$status = $params['status'] ?? '';
			$amount = $params['money'] ?? 0;
			$platSign = $params['platSign'] ?? '';

			if (empty($orderNo)) {
				\think\facade\Log::error('JayaPay withdrawal callback: missing order number');
				echo 'fail';
				return;
			}

			// 查找对应的提现记录
			$withdrawal = model('UserWithdrawals')->where('order_number', $orderNo)->find();
			if (!$withdrawal) {
				\think\facade\Log::error('JayaPay withdrawal callback: order not found - ' . $orderNo);
				echo 'fail';
				return;
			}

			// 获取渠道配置验证签名
			$channel = model('WithdrawalChannel')->where('id', $withdrawal['channel_id'])->find();
			if (!$channel || $channel['mode'] !== 'jaya_pay') {
				\think\facade\Log::error('JayaPay withdrawal channel not found or mode mismatch');
				echo 'fail';
				return;
			}

			// 从配置文件获取JayaPay配置并验证签名
			$jayaPayConfig = $this->getJayaPayConfigForCallback();
			if (!$jayaPayConfig || !$this->verifyJayaPayWithdrawalSign($params, $jayaPayConfig['private_key'] ?? '')) {
				\think\facade\Log::error('JayaPay withdrawal callback sign verify failed');
				echo 'fail';
				return;
			}

			// 验证金额
			$notifyAmount = floatval($amount);
			if (abs($withdrawal['price'] - $notifyAmount) > 0.01) {
				\think\facade\Log::error("JayaPay withdrawal callback: amount mismatch - order: {$withdrawal['price']}, notify: {$notifyAmount}");
				echo 'fail';
				return;
			}

			// 处理回调结果
			if ($status == '2' || $status == 'success') {
				// 代付成功
				$this->handleWithdrawalSuccess($withdrawal['id']);
				echo 'success';
			} else {
				// 代付失败
				$this->handleWithdrawalFailed($withdrawal['id'], '代付失败，状态：' . $status);
				echo 'success';
			}

		} catch (\Exception $e) {
			\think\facade\Log::error('JayaPay withdrawal callback error: ' . $e->getMessage());
			echo 'fail';
		}
	}

	/**
	 * 注意：传统代付没有自动回调，需要人工在后台审核处理
	 * 传统代付的状态更新通过管理后台的财务审核功能完成
	 */

	/**
	 * 处理提现成功
	 */
	private function handleWithdrawalSuccess($withdrawalId)
	{
		try {
			// 开启事务
			model('UserWithdrawals')->startTrans();

			// 更新提现记录状态
			model('UserWithdrawals')->where('id', $withdrawalId)->update([
				'state' => 1, // 成功
				'set_time' => time(),
				'remarks' => '代付成功'
			]);

			// 获取提现记录
			$withdrawal = model('UserWithdrawals')->where('id', $withdrawalId)->find();

			// 更新用户流水记录
			model('TradeDetails')->where('order_number', $withdrawal['order_number'])->update([
				'state' => 1
			]);

			// 更新每日报表
			$reportFormArray = [
				'uid' => $withdrawal['uid'],
				'type' => 2, // 提现
				'price' => $withdrawal['price'],
				'isadmin' => 0
			];
			model('UserDaily')->updateReportForm($reportFormArray);

			// 提交事务
			model('UserWithdrawals')->commit();

			\think\facade\Log::info("Withdrawal success: {$withdrawal['order_number']}, amount: {$withdrawal['price']}");

		} catch (\Exception $e) {
			// 回滚事务
			model('UserWithdrawals')->rollback();
			\think\facade\Log::error("Withdrawal success transaction error: " . $e->getMessage());
		}
	}

	/**
	 * 处理提现失败
	 */
	private function handleWithdrawalFailed($withdrawalId, $reason = '代付失败')
	{
		try {
			// 开启事务
			model('UserWithdrawals')->startTrans();

			// 获取提现记录
			$withdrawal = model('UserWithdrawals')->where('id', $withdrawalId)->find();

			// 更新提现记录状态
			model('UserWithdrawals')->where('id', $withdrawalId)->update([
				'state' => 2, // 失败
				'set_time' => time(),
				'remarks' => $reason
			]);

			// 退还用户余额
			model('UserTotal')->where('uid', $withdrawal['uid'])->setInc('balance', $withdrawal['price']);

			// 添加资金流水
			$userTotal = model('UserTotal')->where('uid', $withdrawal['uid'])->find();
			model('UserTransaction')->insert([
				'uid' => $withdrawal['uid'],
				'type' => 1, // 退款
				'money' => $withdrawal['price'],
				'balance' => $userTotal['balance'],
				'remarks' => '提现失败退款：' . $withdrawal['order_number'],
				'time' => time()
			]);

			// 更新用户流水记录
			model('TradeDetails')->where('order_number', $withdrawal['order_number'])->update([
				'state' => 2
			]);

			// 提交事务
			model('UserWithdrawals')->commit();

			\think\facade\Log::info("Withdrawal failed: {$withdrawal['order_number']}, reason: {$reason}");

		} catch (\Exception $e) {
			// 回滚事务
			model('UserWithdrawals')->rollback();
			\think\facade\Log::error("Withdrawal failed transaction error: " . $e->getMessage());
		}
	}

	/**
	 * 验证WatchPay提现回调签名
	 */
	private function verifyWatchPayWithdrawalSign($params, $secretKey)
	{
		// WatchPay签名验证逻辑
		// 这里需要根据WatchPay的具体签名规则实现
		return true; // 临时返回true，实际需要实现具体验证逻辑
	}

	/**
	 * 验证JayaPay提现回调签名
	 */
	private function verifyJayaPayWithdrawalSign($params, $secretKey)
	{
		// JayaPay签名验证逻辑
		// 这里需要根据JayaPay的具体签名规则实现
		return true; // 临时返回true，实际需要实现具体验证逻辑
	}

	/**
	 * 充值回调处理接口 - 与提现系统保持一致
	 */
	public function rechargeCallback()
	{
		$params = input('post.');

		try {
			// 记录回调日志
			\think\facade\Log::info('Recharge callback received: ' . json_encode($params));

			// 调用第三方支付服务处理回调
			$thirdPayService = new \app\common\service\ThirdPayService();
			$result = $thirdPayService->handleNotify($params);

			if ($result) {
				echo 'success';
			} else {
				echo 'fail';
			}

		} catch (\Exception $e) {
			\think\facade\Log::error('Recharge callback error: ' . $e->getMessage());
			echo 'fail';
		}
	}

	/**
	 * 充值记录
	 */
	public function getRechargeRecord(){
		$data = model('UserRecharge')->getUserRechargeList();
		return json($data);
	}

	//资金明显 流水
	public function FundDetails(){
		$data = model('UserTransaction')->FundDetails();
		return json($data);
	}
	//转账
	public function Transfer(){
		$data = model('UserTransaction')->Transfer();
		return json($data);
	}

	/**
	 * 获取支付方式（兼容旧接口，内部调用新方法）
	 */
	public function getPayTypes()
	{
		// 直接调用新的配置文件方法
		return $this->getPayTypesFromConfig();
	}

	/**
	 * 直接从配置文件获取支付方式（不依赖数据库渠道配置）
	 */
	public function getPayTypesFromConfig()
	{
		$param = input('post.');
		$lang = $param['lang'] ?? 'id';
		$texts = $this->getTexts($lang);

		// 记录调试信息
		\think\facade\Log::info('Transaction getPayTypesFromConfig called with param: ' . json_encode($param));

		try {
			$allPayTypes = [];

			// 1. 先从数据库获取启用的渠道（包含金额限制）
			$rechargeChannels = model('RechangeType')->where(['state' => 1])->field('id,name,mode,type,minPrice,maxPrice')->select();

			if (empty($rechargeChannels)) {
				return json(['code' => 0, 'msg' => $texts['messages']['no_payment_methods'] ?? '暂无可用的支付方式']);
			}

			// 2. 读取配置文件
			$configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
			if (!file_exists($configPath)) {
				$configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
			}

			if (file_exists($configPath)) {
				$paymentConfig = include($configPath);
			} else {
				// 如果配置文件不存在，使用默认配置
				$paymentConfig = [
					'watch_pay' => [
						'enabled' => true,
						'countries' => [
							'ID' => [
								'name' => '印尼',
								'currency' => 'IDR',
								'min_amount' => 50000,
								'max_amount' => ********,
								'enabled' => true,
								'pay_types' => [
									'200' => ['name' => '网银B2C一类', 'type' => 'online', 'enabled' => false],
									'220' => ['name' => '网银B2C二类', 'type' => 'online', 'enabled' => true, 'fee_rate' => 0.045],
									'223' => ['name' => 'QRIS扫码二类', 'type' => 'scan', 'enabled' => true, 'fee_rate' => 0.055],
								]
							],
						],
					],
					'jaya_pay' => [
						'enabled' => true,
						'countries' => [
							'ID' => [
								'name' => '印尼',
								'currency' => 'IDR',
								'min_amount' => 50000,
								'max_amount' => ********,
								'enabled' => true,
							],
						],
						'payment_methods' => [
							'BCA' => ['name' => 'Bank Central Asia(BCA)', 'type' => 'online_banking', 'enabled' => true],
							'OVO' => ['name' => 'OVO', 'type' => 'e_wallet', 'enabled' => true],
							'QRIS' => ['name' => 'QRIS', 'type' => 'qr_code', 'enabled' => true],
						],
					],
				];
			}

		// 3. 遍历数据库中的每个渠道，根据渠道类型生成支付方式
		foreach ($rechargeChannels as $channel) {
			$channelId = $channel['id'];
			$channelName = $channel['name'];
			$channelMode = $channel['mode'];
			$channelType = $channel['type'];

			// 根据渠道模式处理不同的支付方式
			if ($channelMode == 'watchPay' && isset($paymentConfig['watch_pay']['enabled']) && $paymentConfig['watch_pay']['enabled']) {
				$watchPayCountries = $paymentConfig['watch_pay']['countries'] ?? [];

				foreach ($watchPayCountries as $countryCode => $countryConfig) {
					if (!$countryConfig['enabled']) continue;

					$payTypes = $countryConfig['pay_types'] ?? [];
					foreach ($payTypes as $typeCode => $typeConfig) {
						if (!$typeConfig['enabled']) continue;

						// 获取多语言名称
						$localizedName = $this->getLocalizedWatchPayTypeName($typeCode, $lang, $channelName);

						$allPayTypes[] = [
							'recharge_id' => $channelId, // 使用数据库中的真实渠道ID
							'channel_name' => $channelName,
							'code' => $typeCode,
							'name' => $localizedName ?: $typeConfig['name'],
							'type' => $typeConfig['type'],
							'country_code' => $countryCode,
							'country_name' => $texts['countries'][$countryCode] ?? $countryConfig['name'],
							'currency' => $countryConfig['currency'],
							'min_amount' => floatval($channel['minPrice']) * 100, // 转换为分，使用渠道配置
							'max_amount' => floatval($channel['maxPrice']) * 100, // 转换为分，使用渠道配置
							'channel_type' => 'watch_pay',
							'requires_bank_code' => $typeConfig['requires_bank_code'] ?? false // 添加是否需要bank_code字段
						];
					}
				}
			}


			// 处理JayaPay渠道
			elseif ($channelMode == 'jaya_pay' && isset($paymentConfig['jaya_pay']['enabled']) && $paymentConfig['jaya_pay']['enabled']) {
				// 使用新的pay_types配置结构
				$jayaPayTypes = $paymentConfig['jaya_pay']['pay_types'] ?? [];
				$jayaPayCountries = $paymentConfig['jaya_pay']['countries'] ?? [];

				foreach ($jayaPayTypes as $methodCode => $methodConfig) {
					if (!$methodConfig['enabled']) continue;

					// 获取多语言名称
					$localizedName = $this->getLocalizedJayaPayMethodName($methodCode, $lang, $channelName);

					// JayaPay主要支持印尼
					$countryCode = 'ID';
					$countryConfig = $jayaPayCountries[$countryCode] ?? [];

					$allPayTypes[] = [
						'recharge_id' => $channelId, // 使用数据库中的真实渠道ID
						'channel_name' => $channelName,
						'code' => $methodCode,
						'name' => $localizedName ?: $methodConfig['name'],
						'type' => $methodConfig['type'],
						'country_code' => $countryCode,
						'country_name' => $texts['countries'][$countryCode] ?? ($countryConfig['name'] ?? 'Indonesia'),
						'currency' => $countryConfig['currency'] ?? 'IDR',
						'min_amount' => floatval($channel['minPrice']) * 100, // 转换为分，使用渠道配置
						'max_amount' => floatval($channel['maxPrice']) * 100, // 转换为分，使用渠道配置
						'channel_type' => 'jaya_pay',
						'requires_bank_code' => $methodConfig['requires_bank_code'] ?? false // 添加是否需要bank_code字段
					];
				}
			}
		}

			// 如果没有可用的支付方式
			if (empty($allPayTypes)) {
				return json(['code' => 0, 'msg' => $texts['messages']['no_payment_methods'] ?? '暂无可用的支付方式']);
			}

			return json([
				'code' => 1,
				'msg' => $texts['messages']['success'] ?? '成功',
				'data' => $allPayTypes
			]);

		} catch (\Exception $e) {
			\think\facade\Log::error('getPayTypesFromConfig error: ' . $e->getMessage());
			return json([
				'code' => 0,
				'msg' => $texts['messages']['get_pay_types_failed'] ?? '获取支付方式失败'
			]);
		}
	}

	/**
	 * 获取WatchPay支付方式的多语言名称
	 */
	private function getLocalizedWatchPayTypeName($typeCode, $lang, $channelName = 'WatchPay')
	{
		$typeNames = [
			'200' => [
				'cn' => '网银支付',
				'en' => 'Online Banking',
				'id' => 'Internet Banking'
			],
			'201' => [
				'cn' => '便利店支付',
				'en' => 'Convenience Store',
				'id' => 'Convenience Store'
			],
			'202' => [
				'cn' => 'OVO钱包',
				'en' => 'OVO Wallet',
				'id' => 'OVO Wallet'
			],
			'203' => [
				'cn' => 'QRIS扫码',
				'en' => 'QRIS Scan',
				'id' => 'QRIS Scan'
			],
			'220' => [
				'cn' => 'BNI银行',
				'en' => 'BNI Bank',
				'id' => 'BNI Bank'
			],
			'221' => [
				'cn' => 'BRI银行',
				'en' => 'BRI Bank',
				'id' => 'BRI Bank'
			],
			'222' => [
				'cn' => 'CIMB银行',
				'en' => 'CIMB Bank',
				'id' => 'CIMB Bank'
			],
			'223' => [
				'cn' => 'QRIS扫码',
				'en' => 'QRIS Scan',
				'id' => 'QRIS Scan'
			],
			'224' => [
				'cn' => 'MANDIRI银行',
				'en' => 'MANDIRI Bank',
				'id' => 'MANDIRI Bank'
			],
			'225' => [
				'cn' => 'PERMATA银行',
				'en' => 'PERMATA Bank',
				'id' => 'PERMATA Bank'
			],
			'240' => [
				'cn' => '网银支付',
				'en' => 'Online Banking',
				'id' => 'Internet Banking'
			],
			'243' => [
				'cn' => 'QRIS扫码',
				'en' => 'QRIS Scan',
				'id' => 'QRIS Scan'
			]
		];

		$typeName = $typeNames[$typeCode][$lang] ?? null;
		return $typeName ? "{$channelName} - {$typeName}" : null;
	}

	/**
	 * 获取JayaPay支付方式的多语言名称
	 */
	private function getLocalizedJayaPayMethodName($methodCode, $lang, $channelName = 'JayaPay')
	{
		$typeNames = [
			// 银行转账
			'TRANSFER_BCA' => [
				'cn' => 'BCA银行转账',
				'en' => 'BCA Bank Transfer',
				'id' => 'Transfer BCA'
			],
			'TRANSFER_DANA' => [
				'cn' => 'DANA转账',
				'en' => 'DANA Transfer',
				'id' => 'Transfer DANA'
			],
			// 银行网银支付
			'BCA' => [
				'cn' => 'BCA网银',
				'en' => 'BCA Online Banking',
				'id' => 'BCA Online Banking'
			],
			'MANDIRI' => [
				'cn' => 'Mandiri网银',
				'en' => 'Mandiri Online Banking',
				'id' => 'Mandiri Online Banking'
			],
			'BNI' => [
				'cn' => 'BNI网银',
				'en' => 'BNI Online Banking',
				'id' => 'BNI Online Banking'
			],
			'BRI' => [
				'cn' => 'BRI网银',
				'en' => 'BRI Online Banking',
				'id' => 'BRI Online Banking'
			],
			'PERMATA' => [
				'cn' => 'Permata网银',
				'en' => 'Permata Online Banking',
				'id' => 'Permata Online Banking'
			],
			'CIMB' => [
				'cn' => 'CIMB网银',
				'en' => 'CIMB Online Banking',
				'id' => 'CIMB Online Banking'
			],
			'MAYBANK' => [
				'cn' => 'Maybank网银',
				'en' => 'Maybank Online Banking',
				'id' => 'Maybank Online Banking'
			],
			'DANAMON' => [
				'cn' => 'Danamon网银',
				'en' => 'Danamon Online Banking',
				'id' => 'Danamon Online Banking'
			],
			'BSI' => [
				'cn' => 'BSI网银',
				'en' => 'BSI Online Banking',
				'id' => 'BSI Online Banking'
			],
			'BNC' => [
				'cn' => 'BNC网银',
				'en' => 'BNC Online Banking',
				'id' => 'BNC Online Banking'
			],
			// 电子钱包
			'OVO' => [
				'cn' => 'OVO钱包',
				'en' => 'OVO Wallet',
				'id' => 'OVO'
			],
			'DANA' => [
				'cn' => 'DANA钱包',
				'en' => 'DANA Wallet',
				'id' => 'DANA'
			],
			'LINKAJA' => [
				'cn' => 'LINKAJA钱包',
				'en' => 'LINKAJA Wallet',
				'id' => 'LINKAJA'
			],
			'SHOPEEPAY' => [
				'cn' => 'ShopeePay钱包',
				'en' => 'ShopeePay Wallet',
				'id' => 'ShopeePay'
			],
			// 二维码支付
			'QRIS' => [
				'cn' => 'QRIS扫码',
				'en' => 'QRIS Scan',
				'id' => 'QRIS'
			],
			'DANA_QRIS' => [
				'cn' => 'DANA QRIS扫码',
				'en' => 'DANA QRIS Scan',
				'id' => 'DANA QRIS'
			],
			'GOPAY_QRIS' => [
				'cn' => 'GOPAY QRIS扫码',
				'en' => 'GOPAY QRIS Scan',
				'id' => 'GOPAY QRIS'
			],
			// 便利店支付
			'ALFAMART' => [
				'cn' => 'Alfamart便利店',
				'en' => 'Alfamart Store',
				'id' => 'Alfamart'
			]
		];

		$typeName = $typeNames[$methodCode][$lang] ?? null;
		return $typeName ? "{$channelName} - {$typeName}" : null;
	}



	/**
	 * 获取多语言文本（高效版本，遵循现有系统模式）
	 */
	private function getTexts($lang)
	{
		$texts = [];

		// 国家名称
		if($lang=='cn') {
			$texts['countries'] = [
				'ID' => '印尼', 'IN' => '印度', 'TH' => '泰国', 'VN' => '越南', 'MY' => '马来西亚'
			];
			$texts['pay_types'] = [
				'ovo_wallet' => 'OVO钱包', 'qris_scan' => 'QRIS扫码', 'online_banking' => '网银支付',
				'paytm' => 'Paytm钱包', 'upi' => 'UPI支付', 'online_banking_b2c' => '网银B2C',
				'supex' => 'SUPEX扫码', 'truemoney' => 'TrueMoney钱包', 'promptpay' => 'PromptPay扫码',
				'momo' => 'MOMO钱包', 'zalopay' => 'Zalo Pay钱包', 'online_banking_direct' => '网银直连',
				'scan_payment' => '扫码支付', 'ewallet' => '电子钱包', 'online_banking_card' => '网银转卡',
				// 新增支付方式
				'paytm_entertainment' => 'Paytm娱乐', 'paytm_scan' => 'Paytm扫码', 'upi_scan' => 'UPI扫码',
				'upi_wallet' => 'UPI钱包', 'paytm_category1' => 'Paytm跑分一类', 'paytm_native1' => 'Paytm原生一类',
				'upi_category2' => 'UPI跑分二类', 'upi_native2' => 'UPI原生二类', 'upi_category1' => 'UPI跑分一类'
			];
			$texts['messages'] = [
				'success' => '成功',
				'get_pay_types_failed' => '获取支付方式失败'
			];
		} elseif($lang=='tw') {
			$texts['countries'] = [
				'ID' => '印尼', 'IN' => '印度', 'TH' => '泰國', 'VN' => '越南', 'MY' => '馬來西亞'
			];
			$texts['pay_types'] = [
				'ovo_wallet' => 'OVO錢包', 'qris_scan' => 'QRIS掃碼', 'online_banking' => '網銀支付',
				'paytm' => 'Paytm錢包', 'upi' => 'UPI支付', 'online_banking_b2c' => '網銀B2C',
				'supex' => 'SUPEX掃碼', 'truemoney' => 'TrueMoney錢包', 'promptpay' => 'PromptPay掃碼',
				'momo' => 'MOMO錢包', 'zalopay' => 'Zalo Pay錢包', 'online_banking_direct' => '網銀直連',
				'scan_payment' => '掃碼支付', 'ewallet' => '電子錢包', 'online_banking_card' => '網銀轉卡',
				// 新增支付方式
				'paytm_entertainment' => 'Paytm娛樂', 'paytm_scan' => 'Paytm掃碼', 'upi_scan' => 'UPI掃碼',
				'upi_wallet' => 'UPI錢包', 'paytm_category1' => 'Paytm跑分一類', 'paytm_native1' => 'Paytm原生一類',
				'upi_category2' => 'UPI跑分二類', 'upi_native2' => 'UPI原生二類', 'upi_category1' => 'UPI跑分一類'
			];
			$texts['messages'] = [
				'success' => '成功',
				'get_pay_types_failed' => '獲取支付方式失敗'
			];
		} elseif($lang=='id') {
			$texts['countries'] = [
				'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
			];
			$texts['pay_types'] = [
				'ovo_wallet' => 'Dompet OVO', 'qris_scan' => 'Scan QRIS', 'online_banking' => 'Internet Banking',
				'paytm' => 'Dompet Paytm', 'upi' => 'Pembayaran UPI', 'online_banking_b2c' => 'Internet Banking B2C',
				'supex' => 'Scan SUPEX', 'truemoney' => 'Dompet TrueMoney', 'promptpay' => 'Scan PromptPay',
				'momo' => 'Dompet MOMO', 'zalopay' => 'Dompet Zalo Pay', 'online_banking_direct' => 'Internet Banking Langsung',
				'scan_payment' => 'Pembayaran Scan', 'ewallet' => 'Dompet Elektronik', 'online_banking_card' => 'Transfer Kartu Internet Banking',
				// 新增支付方式
				'paytm_entertainment' => 'Paytm Hiburan', 'paytm_scan' => 'Scan Paytm', 'upi_scan' => 'Scan UPI',
				'upi_wallet' => 'Dompet UPI', 'paytm_category1' => 'Paytm Kategori 1', 'paytm_native1' => 'Paytm Native 1',
				'upi_category2' => 'UPI Kategori 2', 'upi_native2' => 'UPI Native 2', 'upi_category1' => 'UPI Kategori 1'
			];
			$texts['messages'] = [
				'success' => 'Berhasil',
				'get_pay_types_failed' => 'Gagal mendapatkan metode pembayaran'
			];
		} elseif($lang=='ms') {
			$texts['countries'] = [
				'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
			];
			$texts['pay_types'] = [
				'ovo_wallet' => 'Dompet OVO', 'qris_scan' => 'Imbas QRIS', 'online_banking' => 'Perbankan dalam talian',
				'paytm' => 'Dompet Paytm', 'upi' => 'Pembayaran UPI', 'online_banking_b2c' => 'Perbankan dalam talian B2C',
				'supex' => 'Imbas SUPEX', 'truemoney' => 'Dompet TrueMoney', 'promptpay' => 'Imbas PromptPay',
				'momo' => 'Dompet MOMO', 'zalopay' => 'Dompet Zalo Pay', 'online_banking_direct' => 'Perbankan dalam talian langsung',
				'scan_payment' => 'Pembayaran imbas', 'ewallet' => 'Dompet elektronik', 'online_banking_card' => 'Pemindahan kad perbankan dalam talian'
			];
			$texts['messages'] = [
				'success' => 'Berjaya',
				'get_pay_types_failed' => 'Gagal mendapatkan kaedah pembayaran',
				'transfer_order_created' => 'Pesanan pemindahan berjaya dibuat, sila hubungi khidmat pelanggan',
				'unsupported_channel' => 'Kaedah pembayaran tidak disokong, sila pilih yang lain',
				'order_create_failed' => 'Gagal membuat pesanan'
			];
		} elseif($lang=='en') {
			$texts['countries'] = [
				'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
			];
			$texts['pay_types'] = [
				'ovo_wallet' => 'OVO Wallet', 'qris_scan' => 'QRIS Scan', 'online_banking' => 'Internet Banking',
				'paytm' => 'Paytm Wallet', 'upi' => 'UPI Payment', 'online_banking_b2c' => 'Internet Banking B2C',
				'supex' => 'SUPEX Scan', 'truemoney' => 'TrueMoney Wallet', 'promptpay' => 'PromptPay Scan',
				'momo' => 'MOMO Wallet', 'zalopay' => 'Zalo Pay Wallet', 'online_banking_direct' => 'Direct Internet Banking',
				'scan_payment' => 'Scan Payment', 'ewallet' => 'Electronic Wallet', 'online_banking_card' => 'Internet Banking Card Transfer',
				// 新增支付方式
				'paytm_entertainment' => 'Paytm Entertainment', 'paytm_scan' => 'Paytm Scan', 'upi_scan' => 'UPI Scan',
				'upi_wallet' => 'UPI Wallet', 'paytm_category1' => 'Paytm Category 1', 'paytm_native1' => 'Paytm Native 1',
				'upi_category2' => 'UPI Category 2', 'upi_native2' => 'UPI Native 2', 'upi_category1' => 'UPI Category 1'
			];
			$texts['messages'] = [
				'success' => 'Success',
				'get_pay_types_failed' => 'Failed to get payment methods',
				'transfer_order_created' => 'Transfer order created successfully, please contact customer service',
				'unsupported_channel' => 'Payment method not supported, please choose another',
				'order_create_failed' => 'Failed to create order'
			];
		} else {
			// 默认印尼语
			$texts['countries'] = [
				'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
			];
			$texts['pay_types'] = [
				'ovo_wallet' => 'Dompet OVO', 'qris_scan' => 'Scan QRIS', 'online_banking' => 'Internet Banking',
				'paytm' => 'Dompet Paytm', 'upi' => 'Pembayaran UPI', 'online_banking_b2c' => 'Internet Banking B2C',
				'supex' => 'Scan SUPEX', 'truemoney' => 'Dompet TrueMoney', 'promptpay' => 'Scan PromptPay',
				'momo' => 'Dompet MOMO', 'zalopay' => 'Dompet Zalo Pay', 'online_banking_direct' => 'Internet Banking Langsung',
				'scan_payment' => 'Pembayaran Scan', 'ewallet' => 'Dompet Elektronik', 'online_banking_card' => 'Transfer Kartu Internet Banking',
				// 新增支付方式
				'paytm_entertainment' => 'Paytm Hiburan', 'paytm_scan' => 'Scan Paytm', 'upi_scan' => 'Scan UPI',
				'upi_wallet' => 'Dompet UPI', 'paytm_category1' => 'Paytm Kategori 1', 'paytm_native1' => 'Paytm Native 1',
				'upi_category2' => 'UPI Kategori 2', 'upi_native2' => 'UPI Native 2', 'upi_category1' => 'UPI Kategori 1'
			];
			$texts['messages'] = [
				'success' => 'Berhasil',
				'get_pay_types_failed' => 'Gagal mendapatkan metode pembayaran',
				'transfer_order_created' => 'Pesanan transfer berhasil dibuat, silakan hubungi layanan pelanggan',
				'unsupported_channel' => 'Metode pembayaran tidak didukung, silakan pilih yang lain',
				'order_create_failed' => 'Gagal membuat pesanan'
			];
		}

		return $texts;
	}

	/**
	 * 统一支付接口 - 根据渠道类型自动分流
	 */
	public function createUnifiedOrder()
	{
		$param = input('post.');
		$lang = $param['lang'] ?? 'id';

		try {
			// 基础参数验证
			$required = ['token', 'recharge_id', 'amount'];
			foreach ($required as $field) {
				if (!isset($param[$field]) || empty($param[$field])) {
					return json(['code' => 0, 'msg' => "缺少必要参数: {$field}"]);
				}
			}

			// 验证用户token
			$userArr = explode(',', auth_code($param['token'], 'DECODE'));
			$uid = $userArr[0] ?? 0;
			if (empty($uid)) {
				return json(['code' => 0, 'msg' => '用户登录信息无效']);
			}

			// 获取渠道信息
			$channel = model('RechangeType')->where(['id' => $param['recharge_id'], 'state' => 1])->find();
			if (!$channel) {
				return json(['code' => 0, 'msg' => '支付渠道不存在或已禁用']);
			}

			// 验证金额范围
			$amount = floatval($param['amount']);
			if ($amount < $channel['minPrice'] || $amount > $channel['maxPrice']) {
				return json(['code' => 0, 'msg' => '充值金额超出限制范围']);
			}

			// 记录统一支付日志
			\think\facade\Log::info('Unified payment request: ' . json_encode([
				'uid' => $uid,
				'recharge_id' => $param['recharge_id'],
				'channel_mode' => $channel['mode'],
				'amount' => $amount,
				'pay_type' => $param['pay_type'] ?? 'default'
			]));

			// 根据渠道类型分流处理
			switch ($channel['mode']) {
				case 'watchPay':
					return $this->handleWatchPayOrder($param, $channel);

				case 'jaya_pay':
					return $this->handleJayaPayOrder($param, $channel);

				case 'turn':
				case 'turn_alipay':
				case 'turn_wx':
					return $this->handleTransferOrder($param, $channel);

				default:
					return $this->handleStandardOrder($param, $channel);
			}

		} catch (\Exception $e) {
			\think\facade\Log::error('Unified payment error: ' . $e->getMessage());
			return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage()]);
		}
	}

	/**
	 * 处理WatchPay订单
	 */
	private function handleWatchPayOrder($param, $channel)
	{
		try {
			// 临时设置POST参数，模拟直接调用
			$originalPost = $_POST;
			$postData = [
				'recharge_id' => $param['recharge_id'],
				'pay_type' => $param['pay_type'] ?? '220',
				'amount' => $param['amount'],
				'token' => $param['token'],
				'lang' => $param['lang'] ?? 'id'
			];

			// 如果前端传递了bank_code，添加到POST数据中
			if (!empty($param['bank_code'])) {
				$postData['bank_code'] = $param['bank_code'];
			}

			$_POST = array_merge($_POST, $postData);

			$watchPayController = new \app\api\controller\WatchPayController();
			$result = $watchPayController->createOrder();

			// 恢复原始POST参数
			$_POST = $originalPost;

			return $this->formatUnifiedResponse($result, 'watchPay');

		} catch (\Exception $e) {
			\think\facade\Log::error('WatchPay order error: ' . $e->getMessage());
			return json(['code' => 0, 'msg' => 'WatchPay订单创建失败：' . $e->getMessage()]);
		}
	}

	/**
	 * 处理JayaPay订单
	 */
	private function handleJayaPayOrder($param, $channel)
	{
		try {
			// 临时设置POST参数
			$originalPost = $_POST;
			$postData = [
				'recharge_id' => $param['recharge_id'],
				'pay_type' => $param['pay_type'] ?? 'BCA',
				'amount' => $param['amount'],
				'token' => $param['token'],
				'lang' => $param['lang'] ?? 'id'
			];

			// 如果前端传递了bank_code，添加到POST数据中
			if (!empty($param['bank_code'])) {
				$postData['bank_code'] = $param['bank_code'];
			}

			$_POST = array_merge($_POST, $postData);

			$jayaPayController = new \app\api\controller\JayaPayController();
			$result = $jayaPayController->createOrder();

			// 恢复原始POST参数
			$_POST = $originalPost;

			return $this->formatUnifiedResponse($result, 'jaya_pay');

		} catch (\Exception $e) {
			\think\facade\Log::error('JayaPay order error: ' . $e->getMessage());
			return json(['code' => 0, 'msg' => 'JayaPay订单创建失败：' . $e->getMessage()]);
		}
	}

	/**
	 * 处理转账类订单（USDT等）
	 */
	private function handleTransferOrder($param, $channel)
	{
		$lang = $param['lang'] ?? 'id';
		$texts = $this->getTexts($lang);

		// 生成转账订单号
		$orderNo = 'TR' . date('YmdHis') . mt_rand(1000, 9999);

		// 获取用户信息
		$userArr = explode(',', auth_code($param['token'], 'DECODE'));
		$uid = $userArr[0];
		$username = $userArr[1] ?? '';

		// 创建转账充值记录
		$rechargeData = [
			'uid' => $uid,
			'username' => $username,
			'order_number' => $orderNo,
			'money' => $param['amount'],
			'daozhang_money' => $param['amount'],
			'fee' => 0,
			'pay_type' => $param['recharge_id'],
			'add_time' => time(),
			'state' => 0, // 待处理
			'type' => 'app',
			'remarks' => '转账充值 - ' . $channel['name']
		];

		$rechargeId = model('UserRecharge')->insertGetId($rechargeData);

		if ($rechargeId) {
			return json([
				'code' => 1,
				'msg' => $texts['messages']['transfer_order_created'] ?? '转账订单创建成功',
				'data' => [
					'order_no' => $orderNo,
					'order_type' => 'transfer',
					'channel_name' => $channel['name'],
					'amount' => $param['amount'],
					'recharge_id' => $rechargeId,
					'instructions' => $this->getTransferInstructions($channel['mode'], $lang)
				]
			]);
		} else {
			return json(['code' => 0, 'msg' => $texts['messages']['order_create_failed'] ?? '订单创建失败']);
		}
	}

	/**
	 * 处理标准渠道订单
	 */
	private function handleStandardOrder($param, $channel)
	{
		$lang = $param['lang'] ?? 'id';
		$texts = $this->getTexts($lang);

		return json([
			'code' => 0,
			'msg' => $texts['messages']['unsupported_channel'] ?? '暂不支持该支付方式，请选择其他方式'
		]);
	}

	/**
	 * 统一响应格式化
	 */
	private function formatUnifiedResponse($result, $channelType)
	{
		// 处理ThinkPHP的Response对象
		if (is_object($result) && method_exists($result, 'getData')) {
			$result = $result->getData();
		}

		// 确保result是数组格式
		if (is_string($result)) {
			$result = json_decode($result, true);
		}

		if (isset($result['code']) && $result['code'] == 1) {
			return json([
				'code' => 1,
				'msg' => $result['msg'] ?? '订单创建成功',
				'data' => [
					'order_no' => $result['data']['order_no'] ?? '',
					'pay_url' => $result['data']['pay_url'] ?? '',
					'amount' => $result['data']['amount'] ?? 0,
					'channel_type' => $channelType,
					'platform_order_no' => $result['data']['platform_order_no'] ?? '',
					'recharge_id' => $result['data']['recharge_id'] ?? ''
				]
			]);
		} else {
			return json([
				'code' => 0,
				'msg' => $result['msg'] ?? '订单创建失败'
			]);
		}
	}

	/**
	 * 获取转账说明
	 */
	private function getTransferInstructions($channelMode, $lang)
	{
		$instructions = [];

		if ($lang == 'cn') {
			switch ($channelMode) {
				case 'turn':
					$instructions = [
						'请联系客服获取转账账户信息',
						'转账完成后请提供转账凭证',
						'客服确认后将为您充值到账'
					];
					break;
				case 'turn_alipay':
					$instructions = [
						'请联系客服获取支付宝收款码',
						'使用支付宝扫码转账',
						'转账完成后请截图发送给客服'
					];
					break;
				case 'turn_wx':
					$instructions = [
						'请联系客服获取微信收款码',
						'使用微信扫码转账',
						'转账完成后请截图发送给客服'
					];
					break;
			}
		} else {
			// 默认英文/印尼语
			switch ($channelMode) {
				case 'turn':
					$instructions = [
						'Please contact customer service for transfer account information',
						'Provide transfer receipt after completion',
						'Account will be credited after verification'
					];
					break;
				case 'turn_alipay':
					$instructions = [
						'Please contact customer service for Alipay QR code',
						'Scan and transfer using Alipay',
						'Send screenshot to customer service after transfer'
					];
					break;
				case 'turn_wx':
					$instructions = [
						'Please contact customer service for WeChat QR code',
						'Scan and transfer using WeChat',
						'Send screenshot to customer service after transfer'
					];
					break;
			}
		}

		return $instructions;
	}

	/**
	 * 统一回调处理接口
	 */
	public function unifiedCallback()
	{
		$params = input('post.');

		try {
			// 记录回调日志
			\think\facade\Log::info('Unified callback received: ' . json_encode($params));

			// 根据回调参数特征判断渠道类型
			$channelType = $this->detectChannelType($params);

			\think\facade\Log::info('Detected channel type: ' . $channelType);

			switch ($channelType) {
				case 'watchPay':
					$watchPayController = new \app\api\controller\WatchPayController();
					return $watchPayController->notify();

				case 'jaya_pay':
					$jayaPayController = new \app\api\controller\JayaPayController();
					return $jayaPayController->notify();

				default:
					\think\facade\Log::error('Unknown callback type: ' . json_encode($params));
					echo 'fail';
					return;
			}

		} catch (\Exception $e) {
			\think\facade\Log::error('Unified callback error: ' . $e->getMessage());
			echo 'fail';
		}
	}

	/**
	 * 检测回调渠道类型
	 */
	private function detectChannelType($params)
	{
		// JayaPay回调特征
		if (isset($params['platOrderNum']) || isset($params['platRespCode']) || isset($params['orderNum'])) {
			return 'jaya_pay';
		}

		// WatchPay回调特征
		if (isset($params['orderid']) || isset($params['status']) || isset($params['trade_status'])) {
			return 'watchPay';
		}

		return 'unknown';
	}

	/**
	 * 统一订单查询接口
	 */
	public function queryUnifiedOrder()
	{
		$param = input('post.');
		$lang = $param['lang'] ?? 'id';

		try {
			// 参数验证
			if (!isset($param['order_no']) || empty($param['order_no'])) {
				return json(['code' => 0, 'msg' => '缺少订单号参数']);
			}

			// 查询充值记录
			$recharge = model('UserRecharge')->where('order_number', $param['order_no'])->find();
			if (!$recharge) {
				return json(['code' => 0, 'msg' => '订单不存在']);
			}

			// 获取渠道信息
			$channel = model('RechangeType')->where('id', $recharge['pay_type'])->find();
			if (!$channel) {
				return json(['code' => 0, 'msg' => '渠道信息不存在']);
			}

			// 返回订单状态
			$statusMap = [
				0 => '待支付',
				1 => '支付成功',
				2 => '支付失败',
				3 => '处理中'
			];

			return json([
				'code' => 1,
				'msg' => '查询成功',
				'data' => [
					'order_no' => $recharge['order_number'],
					'amount' => $recharge['money'],
					'status' => $recharge['state'],
					'status_text' => $statusMap[$recharge['state']] ?? '未知状态',
					'channel_name' => $channel['name'],
					'add_time' => date('Y-m-d H:i:s', $recharge['add_time']),
					'dispose_time' => $recharge['dispose_time'] ? date('Y-m-d H:i:s', $recharge['dispose_time']) : ''
				]
			]);

		} catch (\Exception $e) {
			\think\facade\Log::error('Query unified order error: ' . $e->getMessage());
			return json(['code' => 0, 'msg' => '查询失败：' . $e->getMessage()]);
		}
	}

	/**
	 * 从配置文件获取WatchPay配置（用于回调验证）
	 */
	private function getWatchPayConfigForCallback()
	{
		try {
			$paymentConfig = include(ROOT_PATH . 'config/payment_config.php');

			if (!isset($paymentConfig['watch_pay']) || !$paymentConfig['watch_pay']['enabled']) {
				return false;
			}

			// 获取印尼配置（默认国家）
			$countryConfig = $paymentConfig['watch_pay']['countries']['ID'] ?? null;
			if (!$countryConfig || !$countryConfig['enabled']) {
				return false;
			}

			return [
				'merchant_id' => $countryConfig['merchant_id'],
				'secret_key' => $countryConfig['pay_key'],
				'gateway_url' => $countryConfig['gateway_url'],
				'notify_url' => $countryConfig['notify_domain'],
			];

		} catch (\Exception $e) {
			\think\facade\Log::error('Get WatchPay config for callback error: ' . $e->getMessage());
			return false;
		}
	}

	/**
	 * 从配置文件获取JayaPay配置（用于回调验证）
	 */
	private function getJayaPayConfigForCallback()
	{
		try {
			$paymentConfig = include(ROOT_PATH . 'config/payment_config.php');

			if (!isset($paymentConfig['jaya_pay']) || !$paymentConfig['jaya_pay']['enabled']) {
				return false;
			}

			// 获取默认商户配置
			$merchantConfig = $paymentConfig['jaya_pay']['merchants']['default'] ?? null;
			if (!$merchantConfig || !$merchantConfig['enabled']) {
				return false;
			}

			return [
				'merchant_id' => $merchantConfig['merchant_code'],
				'private_key' => $merchantConfig['private_key'],
				'public_key' => $merchantConfig['public_key'],
				'notify_url' => $merchantConfig['notify_url'],
			];

		} catch (\Exception $e) {
			\think\facade\Log::error('Get JayaPay config for callback error: ' . $e->getMessage());
			return false;
		}
	}

}
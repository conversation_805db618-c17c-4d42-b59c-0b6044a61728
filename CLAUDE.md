# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a PHP-based financial transaction platform built on ThinkPHP 5.1 framework. The application provides payment, recharge, withdrawal, and task management services with multi-language support. It includes both user-facing APIs and administrative management interfaces.

## Development Commands

### Docker Environment (Recommended)
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild containers
docker-compose build --no-cache
```

### PHP Console Commands
```bash
# Run ThinkPHP console commands
php think

# Run automatic audit cron job
php cron_auto_audit.php
```

### Database Management
- Access phpMyAdmin at http://localhost:8082
- Database: `di<PERSON><PERSON>han`
- Root credentials: root/root123456
- Complete database structure in `database_complete.sql`

## Architecture Overview

### Multi-Module Structure
The application uses ThinkPHP's multi-module architecture:

- **api/**: REST API endpoints for frontend/mobile app integration
- **manage/**: Administrative backend interface
- **agent/**: Agent management system
- **index/**: Main website frontend
- **create/**: Utility modules for system operations
- **common/**: Shared models, services, and utilities

### Key Components

**Payment Integration**:
- JayaPay integration (`application/common/library/JayaPaySignUtil.php`)
- WatchPay integration with multi-language support
- Multiple payment channel support

**Core Services**:
- `ThirdPayService`: Third-party payment integration
- `WithdrawalService`: Withdrawal processing
- `MultiLangTradeService`: Multi-language transaction handling

**Database Layer**:
- Models follow ThinkPHP naming conventions (e.g., `UsersModel`, `UserTransactionModel`)
- Comprehensive user management and transaction tracking
- Activity and task management systems

### Configuration Structure

**Environment Configuration**:
- `config/database.php`: Database connection settings (Docker/production aware)
- `config/payment_config.php`: Payment gateway configurations
- `config/third_pay.php`: Third-party payment service settings

**Application Settings**:
- Debug mode enabled in `config/app.php` (line 22)
- Timezone set to Asia/Shanghai
- Multi-language support enabled

## Key Development Patterns

### API Controllers
- Extend `BaseController` for common functionality
- Token-based authentication using `auth_code()` function
- Multi-language support via `lang` parameter
- Consistent JSON response format

### Model Layer
- ThinkPHP ORM with model suffixes (e.g., `UserModel.php`)
- Validation classes in `validate/` directories
- Database relationships handled through model methods

### Payment Flow
- Channel selection via `getRechargetype()` and `getWithdrawaltype()`
- Transaction processing through dedicated controllers
- Automatic audit system via cron job (`cron_auto_audit.php`)

## Environment Detection

The cron system automatically detects environment:
- **Docker**: Uses `http://nginx` for internal service communication
- **Baota**: Production environment with HTTPS
- **Local**: Development setup with localhost

## Security Considerations

- User authentication via encrypted tokens
- IP whitelist support in management interface
- Role-based access control system
- Audit logging for all financial transactions

## File Upload and Media

- Upload directory: `public/upload/`
- Support for images, files, APK packages
- Static assets in `public/static/`

## Multi-Language Support

The platform supports multiple languages including:
- Chinese (cn, zh-CN, zh-TW)
- Indonesian (id-ID) - Default
- English (en-US)
- Thai, Vietnamese, Portuguese, and others

Language-specific assets are stored in `public/static/images/` with language suffixes.
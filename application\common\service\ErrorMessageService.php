<?php
namespace app\common\service;

/**
 * 统一错误信息管理服务
 * 从程序架构层面杜绝错误信息重复和混乱的问题
 */
class ErrorMessageService
{
    // 错误信息模板配置
    private static $errorTemplates = [
        // 提现相关错误
        'withdrawal' => [
            'channel_not_found' => '代付渠道不存在或已禁用',
            'amount_limit_exceeded' => '代付金额超出渠道限制范围',
            'channel_config_error' => '代付渠道配置错误',
            'watchpay_failed' => 'WatchPay代付失败',
            'jayapay_failed' => 'JayaPay代付失败',
            'traditional_failed' => '传统代付状态更新失败',
            'network_error' => '网络连接失败',
            'api_error' => '接口请求失败',
            'config_incomplete' => '渠道配置不完整',
            'processing_error' => '代付处理异常'
        ],
        // 充值相关错误
        'recharge' => [
            'channel_not_found' => '充值渠道不存在或已禁用',
            'amount_invalid' => '充值金额无效',
            'config_error' => '充值渠道配置错误'
        ],
        // 审核相关错误
        'audit' => [
            'financial_failed' => '财务审核失败',
            'control_failed' => '风控审核失败',
            'channel_required' => '审核通过时必须选择代付渠道'
        ]
    ];

    /**
     * 错误信息模板配置
     * 使用错误码映射，避免硬编码错误信息
     */
    private static $errorTemplates = [
        // 提现相关错误
        'withdrawal' => [
            'channel_not_found' => [
                'code' => 'W001',
                'level' => self::LEVEL_BUSINESS,
                'template' => '代付渠道不存在或已禁用',
                'user_message' => '选择的提现渠道暂不可用',
                'admin_message' => '代付渠道配置错误'
            ],
            'amount_limit_exceeded' => [
                'code' => 'W002',
                'level' => self::LEVEL_VALIDATION,
                'template' => '代付金额超出渠道限制范围',
                'user_message' => '提现金额超出限制',
                'admin_message' => '代付金额超出渠道限制范围'
            ],
            'channel_config_error' => [
                'code' => 'W003',
                'level' => self::LEVEL_SYSTEM,
                'template' => '代付渠道配置错误',
                'user_message' => '系统配置异常，请联系客服',
                'admin_message' => '代付渠道配置错误'
            ],
            'watchpay_failed' => [
                'code' => 'W004',
                'level' => self::LEVEL_EXTERNAL,
                'template' => 'WatchPay代付失败：{reason}',
                'user_message' => '代付处理失败，请稍后重试',
                'admin_message' => 'WatchPay代付失败：{reason}'
            ],
            'jayapay_failed' => [
                'code' => 'W005',
                'level' => self::LEVEL_EXTERNAL,
                'template' => 'JayaPay代付失败：{reason}',
                'user_message' => '代付处理失败，请稍后重试',
                'admin_message' => 'JayaPay代付失败：{reason}'
            ],
            'traditional_failed' => [
                'code' => 'W006',
                'level' => self::LEVEL_SYSTEM,
                'template' => '传统代付状态更新失败',
                'user_message' => '处理失败，请联系客服',
                'admin_message' => '传统代付状态更新失败'
            ]
        ],
        // 充值相关错误
        'recharge' => [
            'channel_not_found' => [
                'code' => 'R001',
                'level' => self::LEVEL_BUSINESS,
                'template' => '充值渠道不存在或已禁用',
                'user_message' => '选择的充值渠道暂不可用',
                'admin_message' => '充值渠道配置错误'
            ]
        ]
    ];

    /**
     * 创建错误信息对象
     * @param string $type 错误类型
     * @param string $key 错误键名
     * @param array $params 参数替换
     * @param string $context 上下文信息
     * @return ErrorMessage
     */
    public static function create($type, $key, $params = [], $context = '')
    {
        return new ErrorMessage($type, $key, $params, $context);
    }

    /**
     * 获取错误信息模板
     * @param string $type 错误类型
     * @param string $key 错误键名
     * @return array|null
     */
    public static function getTemplate($type, $key)
    {
        return self::$errorTemplates[$type][$key] ?? null;
    }

    /**
     * 格式化错误信息
     * @param string $template 模板
     * @param array $params 参数
     * @return string
     */
    public static function formatMessage($template, $params = [])
    {
        $message = $template;
        foreach ($params as $key => $value) {
            $message = str_replace('{' . $key . '}', $value, $message);
        }
        return $message;
    }
}

/**
 * 错误信息对象
 * 封装错误信息的所有属性和行为
 */
class ErrorMessage
{
    private $type;
    private $key;
    private $params;
    private $context;
    private $template;

    public function __construct($type, $key, $params = [], $context = '')
    {
        $this->type = $type;
        $this->key = $key;
        $this->params = $params;
        $this->context = $context;
        $this->template = ErrorMessageService::getTemplate($type, $key);
    }

    /**
     * 获取错误码
     */
    public function getCode()
    {
        return $this->template['code'] ?? 'UNKNOWN';
    }

    /**
     * 获取错误级别
     */
    public function getLevel()
    {
        return $this->template['level'] ?? ErrorMessageService::LEVEL_SYSTEM;
    }

    /**
     * 获取原始错误信息（用于日志记录）
     */
    public function getRawMessage()
    {
        if (!$this->template) {
            return "Unknown error: {$this->type}.{$this->key}";
        }
        return ErrorMessageService::formatMessage($this->template['template'], $this->params);
    }

    /**
     * 获取用户友好的错误信息
     */
    public function getUserMessage()
    {
        if (!$this->template) {
            return '系统错误，请稍后重试';
        }
        return ErrorMessageService::formatMessage($this->template['user_message'], $this->params);
    }

    /**
     * 获取管理员错误信息
     */
    public function getAdminMessage()
    {
        if (!$this->template) {
            return "Unknown error: {$this->type}.{$this->key}";
        }
        return ErrorMessageService::formatMessage($this->template['admin_message'], $this->params);
    }

    /**
     * 获取带上下文的完整错误信息
     */
    public function getContextMessage($prefix = '')
    {
        $message = $this->getAdminMessage();
        if ($prefix && $this->context) {
            return $prefix . $message;
        }
        return $message;
    }

    /**
     * 转换为数组格式
     */
    public function toArray()
    {
        return [
            'code' => $this->getCode(),
            'level' => $this->getLevel(),
            'type' => $this->type,
            'key' => $this->key,
            'raw_message' => $this->getRawMessage(),
            'user_message' => $this->getUserMessage(),
            'admin_message' => $this->getAdminMessage(),
            'context' => $this->context
        ];
    }

    /**
     * 转换为字符串（默认返回管理员信息）
     */
    public function __toString()
    {
        return $this->getAdminMessage();
    }
}

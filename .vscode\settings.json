{
    "debug.javascript.defaultRuntimeExecutable": {
        "pwa-node": "c:\\users\\<USER>\\appdata\\local\\mise\\shims\\node"
    },
    
    // PHP调试设置
    "php.validate.enable": true,
    "php.validate.executablePath": "php",
    "php.validate.run": "onSave",
    "php.suggest.basic": true,
    "php.debug.port": [9003],
    
    // 文件关联
    "files.associations": {
        "*.php": "php",
        "*.html": "html",
        "*.js": "javascript",
        "*.css": "css",
        "*.log": "log"
    },
    
    // 排除文件
    "files.exclude": {
        "**/vendor": true,
        "**/node_modules": true,
        "**/.git": true,
        "**/logs/*.log": false
    },
    
    // 搜索排除
    "search.exclude": {
        "**/vendor": true,
        "**/node_modules": true,
        "**/logs": true
    },
    
    // 编辑器设置
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    "editor.renderWhitespace": "boundary",
    "editor.wordWrap": "on",
    
    // 调试设置
    "debug.allowBreakpointsEverywhere": true,
    "debug.showInStatusBar": "always",
    
    // 终端设置
    "terminal.integrated.cwd": "${workspaceFolder}",
    "kiroAgent.configureMCP": "Disabled"
}

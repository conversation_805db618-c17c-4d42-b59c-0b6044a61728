<?php
namespace app\common\service;

use think\facade\Log;
use app\common\constants\PaymentStatus;

// 引入签名工具类
require_once __DIR__ . '/../library/signapi.php';
require_once __DIR__ . '/../library/JayaPaySignUtil.php';

class ThirdPayService
{
    private $config;

    public function __construct()
    {
        $this->config = config('third_pay');
    }

    /**
     * 创建支付订单
     */
    public function createOrder($orderData)
    {
        try {
            \think\facade\Log::info('ThirdPayService createOrder called with: ' . json_encode($orderData));

            $rechargeId = $orderData['recharge_id'];
            $payType = $orderData['pay_type'];

            // 获取对应渠道的商户配置
            $merchantConfig = $this->getMerchantConfig($rechargeId);
            if (!$merchantConfig) {
                \think\facade\Log::error('No merchant config found for recharge_id: ' . $rechargeId);
                return ['code' => 0, 'msg' => '渠道配置错误或未配置商户信息'];
            }

            // 根据渠道类型调用不同的处理方法
            if ($merchantConfig['channel_type'] === 'jaya_pay') {
                return $this->createJayaPayOrder($orderData, $merchantConfig);
            } else {
                return $this->createWatchPayOrder($orderData, $merchantConfig);
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('ThirdPay createOrder error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '系统错误：' . $e->getMessage()];
        }
    }

    /**
     * 处理支付回调 - 统一入口
     */
    public function handleNotify($params)
    {
        try {
            // 判断回调类型
            $isJayaPay = isset($params['platOrderNum']) || isset($params['platRespCode']) || isset($params['platSign']);
            $isWatchPay = isset($params['mchOrderNo']) || isset($params['tradeResult']) || isset($params['merNo']);

            if ($isJayaPay) {
                // JayaPay回调处理
                return $this->handleJayaPayNotify($params);
            } elseif ($isWatchPay) {
                // WatchPay回调处理
                return $this->handleWatchPayNotify($params);
            } else {
                \think\facade\Log::error('Unknown payment callback type: ' . json_encode($params));
                return false;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('ThirdPay handleNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理WatchPay支付回调
     */
    private function handleWatchPayNotify($params)
    {
        try {
            // 验证WatchPay回调签名
            if (!$this->verifyWatchPaySign($params)) {
                \think\facade\Log::error('WatchPay notify sign verify failed');
                return false;
            }

            $orderNo = $params['mchOrderNo'] ?? '';
            $tradeResult = $params['tradeResult'] ?? '';
            $amount = $params['amount'] ?? 0;

            if (empty($orderNo)) {
                \think\facade\Log::error('WatchPay notify: missing order number');
                return false;
            }

            // 查找充值记录
            $recharge = model('UserRecharge')->where('order_number', $orderNo)->find();
            if (!$recharge) {
                \think\facade\Log::error("ThirdPay notify: order not found - {$orderNo}");
                return false;
            }

            // 检查订单状态
            if ($recharge['state'] == PaymentStatus::RECHARGE_SUCCESS) {
                \think\facade\Log::info("ThirdPay notify: order already processed - {$orderNo}");
                return true;
            }

            // 验证金额
            $notifyAmount = floatval($amount);
            if (abs($recharge['money'] - $notifyAmount) > 0.01) {
                \think\facade\Log::error("WatchPay notify: amount mismatch - order: {$recharge['money']}, notify: {$notifyAmount}");
                return false;
            }

            // 根据WatchPay交易结果处理订单
            if ($tradeResult == '1') { // 支付成功
                return $this->processSuccessOrder($recharge, $notifyAmount);
            } else {
                // 支付失败
                model('UserRecharge')->where('id', $recharge['id'])->update([
                    'state' => PaymentStatus::RECHARGE_FAILED,
                    'dispose_time' => time(),
                    'remarks' => 'WatchPay支付失败，交易结果：' . $tradeResult
                ]);
                return true;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('ThirdPay handleNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理成功订单
     */
    private function processSuccessOrder($recharge, $amount)
    {
        try {
            // 开启事务
            model('UserRecharge')->startTrans();

            // 更新充值记录状态
            model('UserRecharge')->where('id', $recharge['id'])->update([
                'state' => PaymentStatus::RECHARGE_SUCCESS,
                'dispose_time' => time(),
                'remarks' => '第三方支付成功',
            ]);

            // 更新用户余额
            $userTotalModel = model('UserTotal');
            $userTotalModel->where('uid', $recharge['uid'])->setInc('balance', $recharge['daozhang_money']);
            $userTotalModel->where('uid', $recharge['uid'])->setInc('total_balance', $recharge['daozhang_money']);

            // 获取更新后的余额
            $newBalance = $userTotalModel->where('uid', $recharge['uid'])->value('balance');

            // 获取用户信息
            $userInfo = model('Users')->where('id', $recharge['uid'])->find();

            // 添加资金流水记录
            $tradeData = [
                'uid' => $recharge['uid'],
                'username' => $userInfo['username'] ?? '',
                'trade_number' => 'TR' . date('YmdHis') . rand(1000, 9999),
                'trade_type' => 1, // 充值
                'trade_amount' => $recharge['daozhang_money'],
                'trade_before_balance' => $newBalance - $recharge['daozhang_money'],
                'account_balance' => $newBalance,
                'remarks' => '第三方支付充值',
                'trade_time' => time(),
                'order_number' => $recharge['order_number'],
                'state' => PaymentStatus::TRADE_SUCCESS, // 成功
            ];
            model('TradeDetails')->insert($tradeData);

            // 处理首充奖励等业务逻辑
            $this->handleFirstRecharge($recharge['uid'], $recharge['money']);

            // 提交事务
            model('UserRecharge')->commit();

            \think\facade\Log::info("ThirdPay success: {$recharge['order_number']}, amount: {$amount}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserRecharge')->rollback();
            \think\facade\Log::error("ThirdPay transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理首充奖励
     */
    private function handleFirstRecharge($uid, $amount)
    {
        // 检查是否首次充值
        $firstRecharge = model('UserRecharge')->where([
            'uid' => $uid,
            'state' => PaymentStatus::RECHARGE_SUCCESS
        ])->count();

        if ($firstRecharge == 1) {
            // 首充奖励逻辑
            \think\facade\Log::info("First recharge for user: {$uid}, amount: {$amount}");
        }
    }

    /**
     * 获取商户配置
     */
    private function getMerchantConfig($rechargeId)
    {
        try {
            // 根据渠道ID获取具体的渠道配置
            $rechargeType = model('RechangeType')->where('id', $rechargeId)->where('state', 1)->find();

            if (!$rechargeType) {
                throw new \Exception('充值渠道不存在或未启用');
            }

            if (!in_array($rechargeType['mode'], ['watchPay', 'global_pay', 'jaya_pay'])) {
                throw new \Exception('渠道类型错误，非支持的第三方支付渠道');
            }

            $merchantConfig = $this->getMerchantConfigFromFile($rechargeType);

            if (!$merchantConfig) {
                throw new \Exception('商户配置信息不完整或未配置');
            }

            return $merchantConfig;

        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get merchant config for recharge_id {$rechargeId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 从配置文件获取商户配置
     */
    private function getMerchantConfigFromFile($rechargeType)
    {
        try {
            // 直接读取配置文件，修复路径问题
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);

            if ($rechargeType['mode'] === 'jaya_pay') {
                // JayaPay配置
                if (!isset($paymentConfig['jaya_pay']['enabled']) || !$paymentConfig['jaya_pay']['enabled']) {
                    return null;
                }

                $jayaConfig = $paymentConfig['jaya_pay'];
                $merchantConfig = $jayaConfig['merchants']['default'] ?? null;

                if ($merchantConfig && $merchantConfig['enabled']) {
                    return [
                        'channel_type' => 'jaya_pay',
                        'merchant_code' => $merchantConfig['merchant_code'],
                        'private_key' => $merchantConfig['private_key'],
                        'public_key' => $merchantConfig['public_key'] ?? '',
                        'notify_url' => $merchantConfig['notify_url'] ?? '',
                        'gateway_urls' => $jayaConfig['gateway_urls'],
                    ];
                }
            } else {
                // WatchPay配置（支持watchPay和global_pay模式）
                if (!isset($paymentConfig['watch_pay']['enabled']) || !$paymentConfig['watch_pay']['enabled']) {
                    return null;
                }

                $watchPayConfig = $paymentConfig['watch_pay'];

                // 目前只支持印尼，直接使用ID配置
                $countryConfig = $watchPayConfig['countries']['ID'] ?? null;
                if (!$countryConfig || !$countryConfig['enabled']) {
                    return null;
                }

                return [
                    'channel_type' => 'watchPay',
                    'merchant_id' => $countryConfig['merchant_id'],
                    'pay_key' => $countryConfig['pay_key'],
                    'gateway_url' => $countryConfig['gateway_url'],
                    'notify_domain' => $countryConfig['notify_domain'],
                ];
            }

            return null;

        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get config from file: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 从渠道类型中获取国家代码
     */
    private function getCountryCodeFromRechargeType($rechargeType)
    {
        // 可以根据渠道名称或其他字段推断国家代码
        // 这里简化处理，默认返回ID（印尼）
        // 实际使用时可以根据具体业务逻辑调整

        if (stripos($rechargeType['name'], '印尼') !== false || stripos($rechargeType['name'], 'Indonesia') !== false) {
            return 'ID';
        } elseif (stripos($rechargeType['name'], '印度') !== false || stripos($rechargeType['name'], 'India') !== false) {
            return 'IN';
        } elseif (stripos($rechargeType['name'], '泰国') !== false || stripos($rechargeType['name'], 'Thailand') !== false) {
            return 'TH';
        } elseif (stripos($rechargeType['name'], '巴西') !== false || stripos($rechargeType['name'], 'Brazil') !== false) {
            return 'BR';
        } elseif (stripos($rechargeType['name'], '越南') !== false || stripos($rechargeType['name'], 'Vietnam') !== false) {
            return 'VN';
        } elseif (stripos($rechargeType['name'], '马来') !== false || stripos($rechargeType['name'], 'Malaysia') !== false) {
            return 'MY';
        }

        // 默认返回印尼
        return 'ID';
    }

    /**
     * 生成签名（使用工具类）
     */
    private function generateSign($params, $key)
    {
        // 移除sign和sign_type参数
        unset($params['sign'], $params['sign_type']);

        // 按键名排序
        ksort($params);

        // 构建签名字符串
        $signStr = '';
        foreach ($params as $k => $v) {
            if ($v !== '' && $v !== null) {
                $signStr .= $k . '=' . $v . '&';
            }
        }
        // 移除最后一个&
        $signStr = rtrim($signStr, '&');

        // 使用工具类生成签名
        return \signapi::sign($signStr, $key);
    }

    /**
     * 验证回调签名（使用工具类）
     */
    private function verifyNotifySign($params)
    {
        $sign = $params['sign'] ?? '';
        if (empty($sign)) {
            return false;
        }

        // 根据商户ID获取密钥
        $mchId = $params['mchId'] ?? '';
        $key = $this->getKeyByMerchantId($mchId);
        if (!$key) {
            return false;
        }

        // 构建签名源字符串
        $signParams = $params;
        unset($signParams['sign'], $signParams['signType']);
        ksort($signParams);

        $signStr = '';
        foreach ($signParams as $k => $v) {
            if ($v !== '' && $v !== null) {
                $signStr .= $k . '=' . $v . '&';
            }
        }
        $signStr = rtrim($signStr, '&');

        // 使用工具类验证签名
        return \signapi::validateSignByKey($signStr, $key, $sign);
    }

    /**
     * 根据商户ID获取密钥
     */
    private function getKeyByMerchantId($mchId)
    {
        try {
            // 直接读取配置文件，修复路径问题
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);

                // 从WatchPay配置中查找对应的商户密钥
                if (isset($paymentConfig['watch_pay']['countries'])) {
                    foreach ($paymentConfig['watch_pay']['countries'] as $countryConfig) {
                        if (isset($countryConfig['merchant_id']) && $countryConfig['merchant_id'] == $mchId) {
                            return $countryConfig['pay_key'] ?? null;
                        }
                    }
                }
            }

            // 如果配置文件中没有找到，使用备用的硬编码映射
            $keyMap = [
                'TEST123456' => 'TESTKEY123456789',                 // 测试商户
                '222888001' => '6QUOUSXE6BCZPW8KZ1LQF7XZARXE69XO', // 印尼
                '222887002' => '8979d78b437948f18c14628ff1ad5f41', // 印度
                '600111001' => '01d38989aa524b099962e19301f4553c', // 泰国
                '222886001' => 'GM4NVMDPPLV3MZGLHDTK3VDJ1PZVUHH2', // 巴西
                '800100001' => 'ae89fc17c9f043858fc03872ca72e8d0', // 越南
                '111887001' => '8ba4b3d14415441aa9fc1eca23093c7c', // 马来西亚
            ];

            return $keyMap[$mchId] ?? null;

        } catch (\Exception $e) {
            \think\facade\Log::error('getKeyByMerchantId error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 发送HTTP请求（使用工具类）
     */
    private function sendRequest($url, $params)
    {
        try {
            // 使用工具类发送POST请求
            $postData = http_build_query($params);
            $response = \signapi::http_post_res($url, $postData);

            if (empty($response)) {
                throw new \Exception("Empty response from payment gateway");
            }

            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                // 如果不是有效的JSON，记录原始响应并返回错误
                \think\facade\Log::error("Invalid JSON response from payment gateway: " . $response);
                throw new \Exception("Invalid JSON response: " . json_last_error_msg());
            }

            return $result;

        } catch (\Exception $e) {
            \think\facade\Log::error("ThirdPay sendRequest error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 创建WatchPay支付订单 - 按照官方文档格式
     */
    private function createWatchPayOrder($orderData, $merchantConfig)
    {
        try {
            // 构建WatchPay请求参数 - 按照官方文档
            $params = [
                'version' => '1.0',
                'mch_id' => $merchantConfig['merchant_id'],
                'notify_url' => $orderData['notify_url'],
                'mch_order_no' => $orderData['order_no'],
                'pay_type' => $orderData['pay_type'],
                'trade_amount' => $orderData['amount'],
                'order_date' => date('Y-m-d H:i:s'),
                'goods_name' => '账户充值',
                'sign_type' => 'MD5'
            ];

            // 根据支付类型配置判断是否需要bank_code
            \think\facade\Log::info('WatchPay checking bank_code requirement for pay_type: ' . $orderData['pay_type']);
            $requiresBankCode = $this->payTypeRequiresBankCode($orderData['pay_type']);
            \think\facade\Log::info('WatchPay pay_type ' . $orderData['pay_type'] . ' requires_bank_code: ' . ($requiresBankCode ? 'true' : 'false'));

            if ($requiresBankCode && !empty($orderData['bank_code'])) {
                \think\facade\Log::info('WatchPay converting bank_code: ' . $orderData['bank_code']);
                // 转换银行名称为WatchPay银行编码
                $bankCode = $this->convertBankNameToWatchPayCode($orderData['bank_code']);
                \think\facade\Log::info('WatchPay conversion result: ' . ($bankCode ?: 'null'));

                if ($bankCode) {
                    $params['bank_code'] = $bankCode;
                    \think\facade\Log::info('WatchPay using bank_code: ' . $bankCode . ' (converted from: ' . $orderData['bank_code'] . ')');
                } else {
                    \think\facade\Log::warning('WatchPay bank_code conversion failed for: ' . $orderData['bank_code']);
                }
            } else {
                \think\facade\Log::info('WatchPay not using bank_code - requires: ' . ($requiresBankCode ? 'true' : 'false') . ', provided: ' . (!empty($orderData['bank_code']) ? 'true' : 'false'));
            }



            // 生成WatchPay MD5签名
            $params['sign'] = $this->generateWatchPaySign($params, $merchantConfig['pay_key']);

            // 记录请求参数调试信息
            Log::info('WatchPay createOrder request params: ' . json_encode($params));

            // 发送WatchPay请求 - 使用新的API地址
            $payUrl = 'https://api.watchglb.com/pay/web';
            $response = $this->sendWatchPayRequest($payUrl, $params);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['respCode']) && $result['respCode'] === 'SUCCESS') {
                    return [
                        'code' => 1,
                        'msg' => 'success',
                        'data' => [
                            'pay_url' => $result['payInfo'],
                            'order_no' => $result['orderNo'],
                            'trade_result' => $result['tradeResult']
                        ]
                    ];
                } else {
                    return [
                        'code' => 0,
                        'msg' => $result['tradeMsg'] ?? 'WatchPay订单创建失败'
                    ];
                }
            } else {
                return ['code' => 0, 'msg' => 'WatchPay接口请求失败'];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay createOrder error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'WatchPay订单创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 创建JayaPay支付订单
     */
    private function createJayaPayOrder($orderData, $merchantConfig)
    {
        try {
            // 直接读取配置文件，修复路径问题
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);
                $jayaPayConfig = $paymentConfig['jaya_pay'] ?? [];
            } else {
                // 使用默认配置
                $jayaPayConfig = [
                    'gateway_urls' => [
                        'cash_in' => 'https://openapi.jayapayment.com/gateway/prepaidOrder',
                    ],
                    'defaults' => [
                        'expiry_period' => '30',
                    ],
                ];
            }

            // 构建JayaPay请求参数（法币代收）
            $params = [
                'merchantCode' => $merchantConfig['merchant_code'],
                'orderType' => '0', // 法币交易
                'orderNum' => $orderData['order_no'],
                'payMoney' => intval($orderData['amount']), // 必须是整数，不能有小数
                'productDetail' => '账户充值',
                'notifyUrl' => $orderData['notify_url'],
                'dateTime' => date('YmdHis'),
                'expiryPeriod' => '30', // 30分钟过期
                'name' => 'User', // 可以从用户信息中获取
                'email' => '<EMAIL>', // 可以从用户信息中获取
                'phone' => '**********', // 可以从用户信息中获取
            ];

            // JayaPay使用method参数指定银行编码（直接支付模式）
            if ($this->payTypeRequiresBankCode($orderData['pay_type']) && !empty($orderData['bank_code'])) {
                // 转换银行名称为JayaPay银行编码
                $bankCode = $this->convertBankNameToJayaPayCode($orderData['bank_code']);
                if ($bankCode) {
                    $params['method'] = $bankCode;
                    \think\facade\Log::info('JayaPay using direct payment mode with method: ' . $bankCode . ' (converted from: ' . $orderData['bank_code'] . ')');
                } else {
                    \think\facade\Log::warning('JayaPay bank_code conversion failed for: ' . $orderData['bank_code'] . ', using cashier mode');
                }
            } else {
                // 支付类型不需要bank_code或没有传递bank_code，使用收银台模式
                // method参数可选，不传递则进入收银台模式
                \think\facade\Log::info('JayaPay using cashier mode (no method specified)');
            }

            // 生成JayaPay RSA签名
            $sign = $this->generateJayaPaySign($params, $merchantConfig['private_key']);
            \think\facade\Log::info('JayaPay sign generated: ' . ($sign ? 'success' : 'failed'));
            \think\facade\Log::info('JayaPay sign length: ' . strlen($sign));
            $params['sign'] = $sign;

            // 发送JayaPay请求 - 使用JSON格式
            $gatewayUrl = config('jaya_pay.gateway_urls.cash_in', 'https://openapi.jayapayment.com/gateway/prepaidOrder');
            $response = $this->sendJayaPayRequest($gatewayUrl, $params);

            if ($response) {
                $result = json_decode($response, true);
                \think\facade\Log::info('JayaPay response: ' . $response);
                if ($result && isset($result['platRespCode']) && $result['platRespCode'] === 'SUCCESS') {
                    return [
                        'code' => 1,
                        'msg' => 'success',
                        'data' => [
                            'pay_url' => $result['url'] ?? '',
                            'order_no' => $result['orderNum'] ?? $orderData['order_no'],
                            'platform_order_no' => $result['platOrderNum'] ?? '',
                        ]
                    ];
                } else {
                    return [
                        'code' => 0,
                        'msg' => $result['platRespMessage'] ?? 'JayaPay订单创建失败'
                    ];
                }
            } else {
                return ['code' => 0, 'msg' => 'JayaPay接口请求失败'];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay createOrder error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '系统错误：' . $e->getMessage()];
        }
    }

    /**
     * 处理JayaPay支付回调
     */
    private function handleJayaPayNotify($params)
    {
        try {
            // 获取回调参数
            $orderNo = $params['orderNum'] ?? '';
            $platOrderNo = $params['platOrderNum'] ?? '';
            $status = $params['status'] ?? '';
            $amount = $params['money'] ?? 0;
            $platSign = $params['platSign'] ?? '';

            if (empty($orderNo)) {
                \think\facade\Log::error('JayaPay notify: missing order number');
                return false;
            }

            // 查找充值记录
            $recharge = model('UserRecharge')->where('order_number', $orderNo)->find();
            if (!$recharge) {
                \think\facade\Log::error("JayaPay notify: order not found - {$orderNo}");
                return false;
            }

            // 检查订单状态
            if ($recharge['state'] == PaymentStatus::RECHARGE_SUCCESS) {
                \think\facade\Log::info("JayaPay notify: order already processed - {$orderNo}");
                return true;
            }

            // 验证金额
            $notifyAmount = floatval($amount);
            if (abs($recharge['money'] - $notifyAmount) > 0.01) {
                \think\facade\Log::error("JayaPay notify: amount mismatch - order: {$recharge['money']}, notify: {$notifyAmount}");
                return false;
            }

            // 根据JayaPay状态处理订单
            if ($status == '2') { // 代付成功
                return $this->processSuccessOrder($recharge, $notifyAmount);
            } else {
                // 支付失败
                model('UserRecharge')->where('id', $recharge['id'])->update([
                    'state' => PaymentStatus::RECHARGE_FAILED,
                    'dispose_time' => time(),
                    'remarks' => 'JayaPay支付失败，状态：' . $status
                ]);
                return true;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay handleNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送WatchPay请求 - 使用form格式
     */
    private function sendWatchPayRequest($url, $params)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200) {
                return $response;
            } else {
                \think\facade\Log::error("WatchPay request failed with HTTP code: {$httpCode}");
                return false;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay request error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送JayaPay请求 - 使用JSON格式
     */
    private function sendJayaPayRequest($url, $params)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200) {
                return $response;
            } else {
                \think\facade\Log::error("JayaPay request failed with HTTP code: {$httpCode}");
                return false;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay request error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成WatchPay MD5签名 - 按照官方文档规则
     */
    private function generateWatchPaySign($params, $secretKey)
    {
        // 移除签名相关字段
        unset($params['sign'], $params['sign_type']);

        // 移除空值参数（空值不参与签名）
        $filteredParams = [];
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null && $value !== 0) {
                $filteredParams[$key] = $value;
            }
        }

        // 按键名ASCII码排序
        ksort($filteredParams);

        // 构建签名字符串 - 按照k=v&k=v格式
        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= $key . '=' . $value . '&';
        }

        // 添加密钥
        $signStr .= 'key=' . $secretKey;

        // 生成MD5签名并转为小写
        $sign = strtolower(md5($signStr));

        // 记录签名调试信息
        Log::info('WatchPay sign debug - signStr: ' . $signStr);
        Log::info('WatchPay sign debug - generated sign: ' . $sign);

        return $sign;
    }

    /**
     * 生成JayaPay RSA签名
     */
    private function generateJayaPaySign($params, $privateKey)
    {
        // 移除签名字段
        unset($params['sign']);

        // 按键名ASCII排序
        ksort($params);

        // 构建签名字符串 - 只取值拼接
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signStr .= $value;
            }
        }

        \think\facade\Log::info('JayaPay sign string: ' . $signStr);
        \think\facade\Log::info('JayaPay private key length: ' . strlen($privateKey));

        // 使用RSA私钥签名
        try {
            // 确保私钥格式正确（添加PEM头部和尾部）
            if (strpos($privateKey, '-----BEGIN') === false) {
                $privateKey = "-----BEGIN PRIVATE KEY-----\n" . chunk_split($privateKey, 64, "\n") . "-----END PRIVATE KEY-----\n";
            }

            $privateKeyResource = openssl_pkey_get_private($privateKey);
            if (!$privateKeyResource) {
                throw new \Exception('Invalid private key');
            }

            // JayaPay使用RSA私钥加密（不是签名），类似Java的privateEncrypt
            // 获取密钥长度用于分块处理
            $keyDetails = openssl_pkey_get_details($privateKeyResource);
            $keySize = $keyDetails['bits'];
            $maxBlock = intval($keySize / 8) - 11; // RSA加密时的最大块大小

            \think\facade\Log::info('JayaPay sign string length: ' . strlen($signStr));
            \think\facade\Log::info('JayaPay key size: ' . $keySize . ', max block: ' . $maxBlock);

            $encrypted = '';
            if (strlen($signStr) <= $maxBlock) {
                // 单块加密
                $result = openssl_private_encrypt($signStr, $encrypted, $privateKeyResource);
                if (!$result) {
                    throw new \Exception('Sign generation failed');
                }
            } else {
                // 分块加密
                $offset = 0;
                $encryptedBlocks = [];
                while ($offset < strlen($signStr)) {
                    $block = substr($signStr, $offset, $maxBlock);
                    $blockEncrypted = '';
                    $result = openssl_private_encrypt($block, $blockEncrypted, $privateKeyResource);
                    if (!$result) {
                        throw new \Exception('Sign generation failed at block ' . count($encryptedBlocks));
                    }
                    $encryptedBlocks[] = $blockEncrypted;
                    $offset += $maxBlock;
                }
                $encrypted = implode('', $encryptedBlocks);
            }

            openssl_free_key($privateKeyResource);
            return base64_encode($encrypted);

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay sign generation error: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * 验证WatchPay回调签名
     */
    private function verifyWatchPaySign($params)
    {
        if (!isset($params['sign'])) {
            return false;
        }

        $sign = $params['sign'];
        unset($params['sign'], $params['sign_type']);

        // 获取商户配置
        $orderNo = $params['mchOrderNo'] ?? '';
        if (empty($orderNo)) {
            return false;
        }

        $recharge = model('UserRecharge')->where('order_number', $orderNo)->find();
        if (!$recharge) {
            return false;
        }

        $rechargeType = model('RechangeType')->where('id', $recharge['pay_type'])->find();
        if (!$rechargeType) {
            return false;
        }

        $config = json_decode(html_entity_decode($rechargeType['config_json'], ENT_QUOTES, 'UTF-8'), true);
        if (!$config || !isset($config['pay_key'])) {
            return false;
        }

        // 生成签名进行比较
        $expectedSign = $this->generateWatchPaySign($params, $config['pay_key']);
        return $sign === $expectedSign;
    }

    /**
     * 检查支付类型是否需要bank_code
     */
    private function payTypeRequiresBankCode($payType)
    {
        try {
            // 使用正确的路径获取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                \think\facade\Log::error('payment_config.php not found at: ' . $configPath);
                return false;
            }

            $paymentConfig = include($configPath);

            // 检查WatchPay配置
            $watchPayTypes = $paymentConfig['watch_pay']['countries']['ID']['pay_types'] ?? [];
            if (isset($watchPayTypes[$payType]['requires_bank_code'])) {
                return $watchPayTypes[$payType]['requires_bank_code'];
            }

            // 检查JayaPay配置（如果有的话）
            $jayaPayTypes = $paymentConfig['jaya_pay']['pay_types'] ?? [];
            if (isset($jayaPayTypes[$payType]['requires_bank_code'])) {
                return $jayaPayTypes[$payType]['requires_bank_code'];
            }

            // 默认返回false（不需要bank_code）
            return false;

        } catch (\Exception $e) {
            \think\facade\Log::error('payTypeRequiresBankCode error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取支付类型配置信息（供前端使用）
     */
    public function getPayTypeConfig($channelType = 'watch_pay')
    {
        try {
            $paymentConfig = include(ROOT_PATH . 'config/payment_config.php');

            if ($channelType === 'watch_pay') {
                return $paymentConfig['watch_pay']['countries']['ID']['pay_types'] ?? [];
            } elseif ($channelType === 'jaya_pay') {
                return $paymentConfig['jaya_pay']['pay_types'] ?? [];
            }

            return [];

        } catch (\Exception $e) {
            Log::error('getPayTypeConfig error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 转换银行名称为WatchPay银行编码
     */
    private function convertBankNameToWatchPayCode($bankName)
    {
        try {
            // 使用正确的路径获取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                \think\facade\Log::error('payment_config.php not found for bank conversion');
                return null;
            }

            $paymentConfig = include($configPath);
            $bankMapping = $paymentConfig['bank_code_mapping'] ?? [];

            // 直接查找银行名称
            if (isset($bankMapping[$bankName]['watchpay'])) {
                return $bankMapping[$bankName]['watchpay'];
            }

            // 如果传递的已经是编码，直接返回
            foreach ($bankMapping as $name => $codes) {
                if (isset($codes['watchpay']) && $codes['watchpay'] === $bankName) {
                    return $bankName;
                }
            }

            return null;

        } catch (\Exception $e) {
            \think\facade\Log::error('convertBankNameToWatchPayCode error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 转换银行名称为JayaPay银行编码
     */
    private function convertBankNameToJayaPayCode($bankName)
    {
        try {
            // 使用正确的路径获取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                \think\facade\Log::error('payment_config.php not found for JayaPay bank conversion');
                return null;
            }

            $paymentConfig = include($configPath);
            $bankMapping = $paymentConfig['bank_code_mapping'] ?? [];

            // 查找银行名称对应的WatchPay编码（JayaPay使用相同的编码格式）
            if (isset($bankMapping[$bankName]['watchpay'])) {
                return $bankMapping[$bankName]['watchpay'];
            }

            // 如果传递的已经是编码，检查是否在JayaPay支持的编码中
            $jayaPayTypes = $paymentConfig['jaya_pay']['pay_types'] ?? [];
            if (isset($jayaPayTypes[$bankName])) {
                return $bankName;
            }

            // 检查是否是WatchPay格式的编码
            foreach ($bankMapping as $name => $codes) {
                if (isset($codes['watchpay']) && $codes['watchpay'] === $bankName) {
                    return $bankName;
                }
            }

            return null;

        } catch (\Exception $e) {
            \think\facade\Log::error('convertBankNameToJayaPayCode error: ' . $e->getMessage());
            return null;
        }
    }

}
